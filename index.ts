import 'reflect-metadata'
import './application'

import { fetchRoutes } from 'helocore'
import Fastify from 'fastify'
import cors from '@fastify/cors'
import MultiPart from '@fastify/multipart'
import RateLimit from '@fastify/rate-limit'
import pino from 'pino'

import config from './src/config'
import { RequestWithTrace } from './src/types'
import helpers from './src/libs/helpers'
import enums from './src/libs/enums'

function main() {

  const fastify = Fastify({
    logger: false,
    requestTimeout: config.REQUEST_TIMEOUT
  })

  fastify.register(cors)
  // fastify.register(RateLimit, {
  //   errorResponseBuilder: function (request, context) {
  //     return {
  //       code: 429,
  //       error: 'Too Many Requests',
  //       message: `Rate limit exceeded, retry in ${context.after}. Try again soon.`,
  //       expiresIn: Math.ceil(context.ttl / 1000)// seconds
  //     }
  //   }
  // })
  fastify.register(MultiPart, {
    addToBody: true,
    limits: {
      fieldNameSize: 100, // Max field name size in bytes
      fieldSize: 100,     // Max field value size in bytes
      fields: 10,         // Max number of non-file fields
      fileSize: config.UPLOAD_SIZE,
      files: 1,           // Max number of file fields
      headerPairs: 2000,  // Max number of header key=>value pairs
    }
  })

  fastify['addHook']('preHandler', (req: RequestWithTrace, reply, done) => {
    req.trace_id = helpers.generateHash(30)

    const url = `${req.protocol}://${req.hostname}`
    const endpoint = req.url.split('?')[0]

    pino().info({
      message: 'trace_id Generated',
      trace_id: req.trace_id,
      url: url + endpoint,
      request: JSON.stringify({
        body: JSON.stringify(Object.keys(req.body || {})),
      }),
      timestamp: new Date()
    })

    done()
  })

  fastify.register((app, _, done) => {
    fetchRoutes(app)
    done()
  }, { prefix: enums.route_prefix })

  fastify.listen({ port: config.PORT }, (err) => {
    if (err) {
      console.log(err)
      return process.exit(1)
    }
    console.log('🟢 Upload-Service is Running on Port ', config.PORT)
  })
}

main()