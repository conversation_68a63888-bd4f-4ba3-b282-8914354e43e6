import 'dotenv/config'

export default {
  PORT: Number(process.env.PORT),
  REQUEST_TIMEOUT: Number(process.env.REQUEST_TIMEOUT),
  MINIO_URL: process.env.MINIO_URL,
  MINIO_PORT: Number(process.env.MINIO_PORT),
  MINIO_ACCES_KEY: process.env.MINIO_ACCES_KEY,
  MINIO_SECRET_KEY: process.env.MINIO_SECRET_KEY,
  UPLOAD_SIZE: Number(process.env.UPLOAD_SIZE) || *********,
  MINIO_BUCKET_NAME: process.env.MINIO_BUCKET_NAME,
  FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID,
  FACEBOOK_APP_TOKEN: process.env.FACEBOOK_APP_TOKEN,
  AWS_S3_REGION: process.env.AWS_S3_REGION,
  AWS_S3_ACCESS_KEY_ID: process.env.AWS_S3_ACCESS_KEY_ID,
  AWS_S3_SECRET_ACCESS_KEY: process.env.AWS_S3_SECRET_ACCESS_KEY,
  CLOUDFLARE_R2_ACCOUNT_ID: process.env.CLOUDFLARE_R2_ACCOUNT_ID,
  CLOUDFLARE_R2_ACCESS_KEY_ID: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
  CLOUDFLARE_R2_SECRET_ACCESS_KEY: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  CLOUDFLARE_R2_BASE_URL: process.env.CLOUDFLARE_R2_BASE_URL,
}