import { injectable } from 'helocore'
import axios from 'axios'
import config from '../config'

@injectable()
export default class FacebookService {
  url = 'https://graph.facebook.com/v18.0/'

  constructor() { }

  async CreateSession(mimetype: string, size: number, fileName: string): Promise<string> {
    const requestConfig = {
      url: this.url + `${config.FACEBOOK_APP_ID}/uploads`,
      method: 'POST',
      params: {
        access_token: config.FACEBOOK_APP_TOKEN,
        file_type: mimetype,
        file_length: size,
        file_name: fileName
      }
    }

    return await axios.request(requestConfig).then(response => response.data.id)
  }

  async UploadFile(endpoint: string, buffer: Buffer): Promise<string> {
    const requestConfig = {
      url: this.url + endpoint,
      method: 'POST',
      headers: {
        'Authorization': `OAuth ${process.env.FACEBOOK_APP_TOKEN}`,
        'file_offset': '0'
      },
      data: buffer
    }

    return await axios.request(requestConfig).then(response => response.data.h).catch(err => {
      console.log(err)
    })
  }

  async UploadAdcreativeImage(adaccountId:string,bytes:string): Promise<string> {
    const requestConfig = {
      url: `https://graph.facebook.com/v18.0/act_${adaccountId}/adimages`,
      method: 'POST',
      params: {
        access_token: config.FACEBOOK_APP_TOKEN,
        bytes: bytes
      }
    }
    return await axios.request(requestConfig).then(response => response.data.id)
  }
}
