import { injectable } from 'helocore'

import { TFile } from '../types/upload'
import IUploadService from '../interfaces/IUploadService'
import Minio from '../modules/minio'
import helpers from '../libs/helpers'
import config from '../config'
import FacebookApiService from './FacebookApiService'

@injectable()
export default class UploadService implements IUploadService {
  constructor(
    private readonly minioModule: Minio,
    private readonly facebookApiService: FacebookApiService
  ) { }

  async PutFile(file: TFile, bucketName: string = config.MINIO_BUCKET_NAME): Promise<string> {
    const fileName = helpers.generateHash(10) + file.filename
    await this.minioModule.putFile(fileName, file, bucketName)

    return this.GetFileUrl(fileName, bucketName)
  }

  async GetFileUrl(fileName: string, bucketName: string): Promise<string> {
    return this.minioModule.getFileUrl(fileName, bucketName)
  }

  async RemoveFile(fileName: string, bucketName: string = config.MINIO_BUCKET_NAME): Promise<void> {
    await this.minioModule.removeFile(fileName, bucketName)
  }

  async FBUpload(file: TFile): Promise<string> {
    const endpoint = await this.facebookApiService.CreateSession(file.mimetype, file.data.length, file.filename)

    return this.facebookApiService.UploadFile(endpoint, file.data)
  }
  async MetaUploadAdcreativeImage(file: TFile): Promise<string> {
    //TODO:
  }
}
